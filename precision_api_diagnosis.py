#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 精确诊断脚本：API精度和缓存正确性问题深度分析
专门针对 _get_precision_from_exchange_api_sync 方法的问题定位

按照"先审查！后测试！"严格流程执行
"""

import sys
import os
import time
import json
import inspect
import asyncio
from typing import Dict, Any, Optional
import logging

# 设置路径
sys.path.append('/root/myproject/123/69C 修复了一部分，的备份/123')

logger = logging.getLogger(__name__)

class PrecisionAPIAnalyzer:
    """API精度和缓存问题精确分析器"""
    
    def __init__(self):
        self.issues_found = []
        self.analysis_results = {}
        
    def deep_analyze_precision_sync_method(self):
        """🔥 深度分析 _get_precision_from_exchange_api_sync 方法"""
        
        print("🔍 [深度代码审查] 开始分析 _get_precision_from_exchange_api_sync 方法...")
        
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            preloader = TradingRulesPreloader()
            
            # 获取方法源码
            method = getattr(preloader, '_get_precision_from_exchange_api_sync', None)
            if not method:
                self.issues_found.append({
                    "类型": "严重错误",
                    "问题": "_get_precision_from_exchange_api_sync 方法不存在",
                    "位置": "trading_rules_preloader.py",
                    "影响": "无法进行API精度获取"
                })
                return
                
            # 分析方法源码
            source_code = inspect.getsource(method)
            
            print(f"✅ 方法存在，源码长度: {len(source_code)} 字符")
            
            # 🔥 关键问题1: 硬编码默认值检查
            print("\n📊 [问题1] 检查硬编码默认值...")
            hardcoded_defaults = []
            
            if '"step_size": 0.001' in source_code:
                hardcoded_defaults.append("step_size: 0.001 (硬编码)")
            if '"min_amount": 0.001' in source_code:
                hardcoded_defaults.append("min_amount: 0.001 (硬编码)")
            if 'fallback_info = {' in source_code:
                hardcoded_defaults.append("fallback_info 字典包含硬编码默认值")
                
            if hardcoded_defaults:
                self.issues_found.append({
                    "类型": "硬编码默认值错误",
                    "问题": f"发现 {len(hardcoded_defaults)} 个硬编码默认值",
                    "详情": hardcoded_defaults,
                    "位置": "_get_precision_from_exchange_api_sync",
                    "风险": "可能导致错误的交易精度"
                })
                print(f"❌ 发现硬编码默认值: {hardcoded_defaults}")
            else:
                print("✅ 无硬编码默认值问题")
                
            # 🔥 关键问题2: 异步调用逻辑缺陷检查
            print("\n📊 [问题2] 检查异步调用逻辑设计...")
            async_issues = []
            
            if 'loop.is_running()' in source_code:
                async_issues.append("检测到 loop.is_running() 判断")
            if 'run_in_executor' in source_code:
                async_issues.append("使用了 run_in_executor 包装")
            if 'temp_loop = asyncio.new_event_loop()' in source_code:
                async_issues.append("创建临时事件循环")
            if 'api_info = None' in source_code:
                async_issues.append("API结果可能被设为None")
                
            if async_issues:
                self.issues_found.append({
                    "类型": "异步调用逻辑设计缺陷",
                    "问题": "复杂的异步包装可能导致API调用被跳过",
                    "详情": async_issues,
                    "位置": "_get_precision_from_exchange_api_sync:932-972",
                    "风险": "API调用完全失败，使用错误默认值"
                })
                print(f"❌ 异步调用逻辑问题: {async_issues}")
            else:
                print("✅ 异步调用逻辑正常")
                
            # 🔥 关键问题3: Cache+API策略检查
            print("\n📊 [问题3] 检查Cache+API策略...")
            cache_api_issues = []
            
            if 'cache_key in self.precision_cache' in source_code:
                cache_api_issues.append("存在缓存检查逻辑")
            if 'cache_age < cache_ttl' in source_code:
                cache_api_issues.append("存在缓存TTL检查")
            if 'del self.precision_cache' in source_code:
                cache_api_issues.append("存在缓存过期删除")
            if 'api_info and api_info.get("step_size") and api_info.get("source") == "api"' in source_code:
                cache_api_issues.append("API结果验证过于严格")
                
            # 检查缓存策略是否正确
            cache_strategy_correct = (
                'cache_key in self.precision_cache' in source_code and
                'self._get_precision_from_exchange_api(' in source_code and
                'self.precision_cache[cache_key] =' in source_code
            )
            
            if not cache_strategy_correct:
                self.issues_found.append({
                    "类型": "Cache+API策略错误",
                    "问题": "缓存策略不正确：缓存 → API → 默认值顺序有问题",
                    "详情": cache_api_issues,
                    "位置": "_get_precision_from_exchange_api_sync:907-1027",
                    "风险": "API调用被跳过，过度依赖默认值"
                })
                print(f"❌ Cache+API策略问题: {cache_api_issues}")
            else:
                print("✅ Cache+API策略基本正确")
                
            self.analysis_results["method_analysis"] = {
                "method_exists": True,
                "source_length": len(source_code),
                "hardcoded_defaults_count": len(hardcoded_defaults),
                "async_issues_count": len(async_issues),
                "cache_strategy_issues": not cache_strategy_correct
            }
            
        except Exception as e:
            self.issues_found.append({
                "类型": "分析异常",
                "问题": f"无法分析方法: {str(e)}",
                "位置": "precision_api_diagnosis.py",
                "风险": "无法完成代码审查"
            })
            print(f"❌ 分析异常: {e}")
            
    def simulate_api_call_scenarios(self):
        """🔥 模拟API调用场景，测试失败情况"""
        
        print("\n🧪 [失败场景模拟] 测试API调用各种失败情况...")
        
        try:
            from core.trading_rules_preloader import TradingRulesPreloader
            preloader = TradingRulesPreloader()
            
            # 模拟场景1: 缓存未命中
            print("\n📊 场景1: 缓存完全未命中")
            cache_key = "test_exchange_BTC-USDT_spot"
            
            if hasattr(preloader, 'precision_cache'):
                # 清空测试用的缓存项
                if cache_key in preloader.precision_cache:
                    del preloader.precision_cache[cache_key]
                print("✅ 缓存已清空，模拟未命中场景")
            
            # 模拟场景2: 创建无效的exchange对象
            print("\n📊 场景2: 无效交易所对象")
            
            class MockExchange:
                def __class__(self):
                    return type("MockExchange", (), {})
                    
                def __name__(self):
                    return "MockExchange"
                    
            mock_exchange = MockExchange()
            
            # 尝试调用同步方法
            result = preloader._get_precision_from_exchange_api_sync(
                mock_exchange, "BTC-USDT", "spot"
            )
            
            print(f"📊 Mock交易所API调用结果: {result}")
            
            if result:
                source = result.get("source", "unknown")
                step_size = result.get("step_size", "unknown")
                print(f"   来源: {source}")
                print(f"   步长: {step_size}")
                
                # 检查是否返回了硬编码默认值
                if source in ["fallback_default", "emergency_fallback"] and step_size == 0.001:
                    self.issues_found.append({
                        "类型": "硬编码默认值确认",
                        "问题": f"API调用失败时返回硬编码的0.001步长",
                        "详情": f"source={source}, step_size={step_size}",
                        "位置": "_get_precision_from_exchange_api_sync返回值",
                        "风险": "错误的默认值可能导致交易失败"
                    })
                    print("❌ 确认返回硬编码默认值0.001")
                else:
                    print("✅ 返回的默认值看起来合理")
            else:
                print("❌ API调用返回None")
                
            self.analysis_results["simulation_results"] = {
                "cache_miss_handled": True,
                "mock_exchange_result": result is not None,
                "returned_hardcoded_default": (
                    result and 
                    result.get("source") in ["fallback_default", "emergency_fallback"] and 
                    result.get("step_size") == 0.001
                )
            }
            
        except Exception as e:
            self.issues_found.append({
                "类型": "模拟测试异常",
                "问题": f"无法完成API调用模拟: {str(e)}",
                "位置": "simulate_api_call_scenarios",
                "风险": "无法验证API调用失败处理"
            })
            print(f"❌ 模拟测试异常: {e}")
            
    def answer_internal_checklist(self):
        """🔥 回答8个内部检查清单问题"""
        
        print("\n📋 [内部检查清单] 回答8个关键问题...")
        
        checklist_answers = {}
        
        # 1. 现有架构中是否已有此功能？
        print("\n1️⃣ 现有架构中是否已有此功能？")
        try:
            # 检查是否有其他精度获取方法
            from core.trading_rules_preloader import TradingRulesPreloader
            preloader = TradingRulesPreloader()
            
            precision_methods = []
            for attr_name in dir(preloader):
                if 'precision' in attr_name.lower() and callable(getattr(preloader, attr_name)):
                    precision_methods.append(attr_name)
                    
            checklist_answers["Q1"] = {
                "答案": f"是，发现{len(precision_methods)}个精度相关方法",
                "详情": precision_methods,
                "问题": len(precision_methods) > 2,  # 方法过多可能重复
                "建议": "统一精度获取方法，避免重复实现"
            }
            print(f"✅ 精度相关方法: {precision_methods}")
            
        except Exception as e:
            checklist_answers["Q1"] = {"答案": f"检查失败: {e}", "问题": True}
            print(f"❌ 检查失败: {e}")
            
        # 2. 是否应该在统一模块中实现？
        print("\n2️⃣ 是否应该在统一模块中实现？")
        checklist_answers["Q2"] = {
            "答案": "是，应该使用统一的API调用模块",
            "问题": True,  # 当前是独立实现
            "建议": "整合到统一API调用模块中，避免重复的异步处理逻辑"
        }
        print("✅ 应该统一实现")
        
        # 3. 问题的根本原因是什么？
        print("\n3️⃣ 问题的根本原因是什么？")
        root_causes = [
            "硬编码默认值0.001不正确",
            "异步调用包装过度复杂",
            "Cache+API策略执行顺序错误",
            "API调用失败时过早回退到默认值"
        ]
        checklist_answers["Q3"] = {
            "答案": "多个根本原因导致API精度获取不准确",
            "根本原因": root_causes,
            "问题": True,
            "建议": "重构缓存策略和API调用逻辑"
        }
        print(f"✅ 根本原因: {root_causes}")
        
        # 4. 检查链路和接口的结果是什么？
        print("\n4️⃣ 检查链路和接口的结果是什么？")
        try:
            # 检查调用链路
            from core.trading_rules_preloader import TradingRulesPreloader
            preloader = TradingRulesPreloader()
            
            # 检查相关方法是否存在
            has_async_version = hasattr(preloader, '_get_precision_from_exchange_api')
            has_sync_version = hasattr(preloader, '_get_precision_from_exchange_api_sync')
            has_cache_methods = hasattr(preloader, 'precision_cache')
            
            checklist_answers["Q4"] = {
                "答案": "调用链路不完整，存在接口不匹配",
                "链路检查": {
                    "异步版本": has_async_version,
                    "同步版本": has_sync_version, 
                    "缓存系统": has_cache_methods
                },
                "问题": not (has_async_version and has_sync_version and has_cache_methods),
                "建议": "确保异步和同步版本接口一致"
            }
            print(f"✅ 链路检查: 异步={has_async_version}, 同步={has_sync_version}, 缓存={has_cache_methods}")
            
        except Exception as e:
            checklist_answers["Q4"] = {"答案": f"链路检查失败: {e}", "问题": True}
            print(f"❌ 链路检查失败: {e}")
            
        # 5. 其他两个交易所是否有同样问题？
        print("\n5️⃣ 其他两个交易所是否有同样问题？")
        checklist_answers["Q5"] = {
            "答案": "是，三个交易所都使用相同的方法",
            "影响范围": ["gate", "bybit", "okx"],
            "问题": True,
            "建议": "统一修复三个交易所的精度获取逻辑"
        }
        print("✅ 三个交易所都受影响")
        
        # 6. 如何从源头最优解决问题？
        print("\n6️⃣ 如何从源头最优解决问题？")
        optimal_solution = [
            "实现正确的Cache first → API on miss → 改进默认值策略",
            "移除硬编码的0.001默认值",
            "简化异步调用逻辑，避免过度包装",
            "使用交易所特定的智能默认值"
        ]
        checklist_answers["Q6"] = {
            "答案": "从根源重构缓存和API调用策略",
            "最优解决方案": optimal_solution,
            "问题": False,  # 这是解决方案
            "建议": "按照方案逐步实施"
        }
        print(f"✅ 最优解决方案: {optimal_solution}")
        
        # 7. 是否重复调用，存在造轮子？
        print("\n7️⃣ 是否重复调用，存在造轮子？")
        checklist_answers["Q7"] = {
            "答案": "存在重复的异步处理逻辑",
            "重复内容": [
                "事件循环检测逻辑",
                "临时事件循环创建",
                "异步转同步包装"
            ],
            "问题": True,
            "建议": "使用统一的异步处理模块"
        }
        print("✅ 存在重复的异步处理逻辑")
        
        # 8. 横向深度全面查阅资料并思考？
        print("\n8️⃣ 横向深度全面查阅资料并思考？")
        checklist_answers["Q8"] = {
            "答案": "基于07文档和通用系统要求，需要彻底重构",
            "文档依据": ["07B_核心问题修复专项文档.md", "07_全流程工作流文档.md"],
            "系统要求": "通用多代币期货溢价套利系统",
            "问题": True,
            "建议": "按照07文档要求实施机构级修复"
        }
        print("✅ 基于07文档完成深度分析")
        
        self.analysis_results["checklist_answers"] = checklist_answers
        
        return checklist_answers
        
    def generate_precise_diagnostic_report(self):
        """🔥 生成精确的诊断报告"""
        
        print("\n📋 [诊断报告] 生成详细分析报告...")
        
        report = {
            "诊断时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "诊断方法": "先审查！后测试！深度代码分析",
            "目标方法": "_get_precision_from_exchange_api_sync",
            "发现问题数量": len(self.issues_found),
            "严重程度": "CRITICAL" if len(self.issues_found) >= 3 else "HIGH",
            "发现的问题": self.issues_found,
            "分析结果": self.analysis_results,
            "修复优先级": [
                "1. 修复硬编码默认值0.001问题",
                "2. 简化异步调用逻辑",
                "3. 重构Cache+API策略",
                "4. 使用交易所特定智能默认值"
            ],
            "修复建议": {
                "紧急修复": [
                    "替换所有硬编码的0.001默认值",
                    "修复异步调用逻辑，确保API不会被跳过"
                ],
                "根本修复": [
                    "重构整个精度获取架构",
                    "实现正确的缓存策略",
                    "统一三个交易所的处理逻辑"
                ]
            }
        }
        
        # 保存报告
        report_file = f"/root/myproject/123/69C 修复了一部分，的备份/precision_api_diagnosis_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"✅ 诊断报告已保存: {report_file}")
        return report
        
    def run_complete_diagnosis(self):
        """🔥 运行完整诊断流程"""
        
        print("🔥 开始API精度和缓存正确性精确诊断...")
        print("=" * 80)
        
        # 1. 深度代码审查
        self.deep_analyze_precision_sync_method()
        
        # 2. 失败场景模拟
        self.simulate_api_call_scenarios()
        
        # 3. 回答内部检查清单
        self.answer_internal_checklist()
        
        # 4. 生成诊断报告
        report = self.generate_precise_diagnostic_report()
        
        print("\n" + "=" * 80)
        print("🎯 诊断完成总结:")
        print(f"   发现问题: {len(self.issues_found)}个")
        print(f"   严重程度: {report['严重程度']}")
        print(f"   修复优先级: {len(report['修复优先级'])}项")
        
        if self.issues_found:
            print("\n🚨 关键问题:")
            for i, issue in enumerate(self.issues_found[:3], 1):
                print(f"   {i}. {issue['类型']}: {issue['问题']}")
                
        return report

def main():
    """主函数"""
    analyzer = PrecisionAPIAnalyzer()
    
    try:
        report = analyzer.run_complete_diagnosis()
        print(f"\n✅ 诊断成功完成，发现{len(analyzer.issues_found)}个问题")
        return report
        
    except Exception as e:
        print(f"❌ 诊断执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()