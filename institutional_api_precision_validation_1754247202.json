{"validation_time": "2025-08-03 20:53:22", "total_tests": 13, "success_count": 0, "success_rate": 0.0, "quality_grade": "C 不合格", "deploy_ready": false, "phase_results": {"phase1_basic_core": [{"test_name": "智能代币识别测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6031442}, {"test_name": "缓存TTL机制测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6062639}, {"test_name": "简化异步逻辑测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6087132}, {"test_name": "四层精度获取策略测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6107771}, {"test_name": "边界条件处理测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6127734}], "phase2_system_cascade": [{"test_name": "多交易所一致性测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6150718}, {"test_name": "多币种切换测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6175294}, {"test_name": "缓存与API协调测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6199198}, {"test_name": "状态联动测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6220508}], "phase3_production_simulation": [{"test_name": "高并发压力测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6239948}, {"test_name": "网络波动模拟测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6262417}, {"test_name": "异常恢复测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6282036}, {"test_name": "长时间运行稳定性测试", "passed": false, "details": {"error": "cannot import name 'log_balance_api' from 'utils.cache_monitor' (/root/myproject/123/69C 修复了一部分，的备份/123/utils/cache_monitor.py)"}, "timestamp": 1754247202.6300905}]}}