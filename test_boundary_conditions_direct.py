#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试边界条件处理的简化版本
"""

import sys
import os
import time
sys.path.append('/root/myproject/123/69C 修复了一部分，的备份/123')

# 设置环境变量避免配置问题
os.environ['TARGET_SYMBOLS'] = 'BTC-USDT,ETH-USDT'
os.environ['BYBIT_API_KEY'] = 'test'
os.environ['BYBIT_API_SECRET'] = 'test'
os.environ['GATE_API_KEY'] = 'test'
os.environ['GATE_API_SECRET'] = 'test'

def test_boundary_conditions_direct():
    """直接测试边界条件处理"""
    try:
        from core.trading_rules_preloader import TradingRulesPreloader
        preloader = TradingRulesPreloader()
        
        # 边界条件测试用例
        boundary_cases = [
            (None, "BTC-USDT", "spot", "None exchange"),
            ("mock", None, "spot", "None symbol"),  
            ("mock", "BTC-USDT", None, "None market_type"),
            ("", "BTC-USDT", "spot", "空字符串 exchange"),
            ("mock", "", "spot", "空symbol"),
            ("mock", "INVALID_FORMAT", "spot", "无效格式"),
        ]
        
        class MockExchange:
            def __class__(self):
                return type("MockExchange", (), {})
            def __name__(self):
                return "MockExchange"
        
        mock_exchange = MockExchange()
        results = []
        
        print("测试边界条件处理:")
        print("=" * 60)
        
        for i, (exchange, symbol, market_type, description) in enumerate(boundary_cases):
            print(f"\n测试用例 {i+1}: {description}")
            print(f"  参数: exchange={exchange}, symbol={symbol}, market_type={market_type}")
            
            try:
                if exchange is None:
                    result = preloader._get_precision_from_exchange_api_sync(None, symbol, market_type)
                elif exchange == "":
                    result = preloader._get_precision_from_exchange_api_sync("", symbol, market_type)
                else:
                    result = preloader._get_precision_from_exchange_api_sync(mock_exchange, symbol, market_type)
                
                print(f"  结果: {result}")
                
                # 判断是否正确处理
                if exchange is None or symbol is None or market_type is None:
                    # 真正的None参数，应该返回None或默认值
                    is_handled = result is None or (result and result.get("step_size") == 0.01)
                elif exchange == "" or symbol == "":
                    # 空字符串，应该返回默认值或None  
                    is_handled = result is None or (result and result.get("step_size") in [0.01, 0.001, 0.00001, 1.0, 0.1])
                else:
                    # 无效格式，应该返回智能默认值
                    is_handled = result is not None and result.get("step_size") in [0.01, 0.001, 0.00001, 1.0, 0.1]
                
                print(f"  处理正确: {'✅' if is_handled else '❌'}")
                results.append(is_handled)
                
            except Exception as e:
                print(f"  异常: {e}")
                # 对于边界条件，抛出异常也算合理处理
                results.append(True)
        
        print(f"\n总结:")
        print(f"  测试用例数: {len(boundary_cases)}")
        print(f"  正确处理: {sum(results)}")
        print(f"  通过率: {sum(results)}/{len(boundary_cases)} = {sum(results)/len(boundary_cases)*100:.1f}%")
        
        # 详细分析每个失败的用例
        print(f"\n失败用例分析:")
        for i, (is_handled, (exchange, symbol, market_type, description)) in enumerate(zip(results, boundary_cases)):
            if not is_handled:
                print(f"  用例 {i+1} 失败: {description}")
                print(f"    参数: exchange={exchange}, symbol={symbol}, market_type={market_type}")
                
        return results
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_boundary_conditions_direct()