#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io WebSocket Symbol变量Bug修复验证脚本
===============================================

验证修复是否成功，确保变量定义顺序正确
"""

import sys
import os
import json
import time
from typing import Dict, Any

# 添加项目路径
project_root = "/root/myproject/123/70 gate和okx还是数据阻塞/123"
sys.path.insert(0, project_root)

def verify_gate_ws_fix():
    """验证Gate.io WebSocket修复是否成功"""
    print("🔍 Gate.io WebSocket Symbol变量Bug修复验证")
    print("=" * 50)
    
    gate_ws_file = os.path.join(project_root, "websocket", "gate_ws.py")
    
    if not os.path.exists(gate_ws_file):
        print(f"❌ 文件不存在: {gate_ws_file}")
        return False
    
    with open(gate_ws_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print("📋 修复验证结果:")
    print("-" * 30)
    
    # 查找关键行
    symbol_usage_line = None
    symbol_definition_line = None
    
    for i, line in enumerate(lines, 1):
        if "_log_data_received" in line and "symbol" in line and "gate" in line:
            symbol_usage_line = i
            print(f"🔍 第{i}行 - symbol变量使用: {line.strip()}")
        
        if line.strip().startswith('symbol = ""') and i < 400:  # 在函数开始附近
            symbol_definition_line = i
            print(f"🔍 第{i}行 - symbol变量定义: {line.strip()}")
    
    print("\n📊 验证结果:")
    print("-" * 20)
    
    if symbol_usage_line and symbol_definition_line:
        if symbol_definition_line < symbol_usage_line:
            print(f"✅ 修复成功: symbol变量定义在使用之前")
            print(f"   定义位置: 第{symbol_definition_line}行")
            print(f"   使用位置: 第{symbol_usage_line}行")
            print(f"   顺序差: 定义先于使用{symbol_usage_line - symbol_definition_line}行")
            return True
        else:
            print(f"❌ 修复失败: symbol变量仍在定义前使用")
            return False
    else:
        print(f"⚠️ 未找到关键代码行")
        return False

def check_code_structure():
    """检查代码结构是否符合预期"""
    print("\n🏗️ 代码结构检查")
    print("=" * 20)
    
    gate_ws_file = os.path.join(project_root, "websocket", "gate_ws.py")
    
    with open(gate_ws_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复标记
    checks = [
        ("**CRITICAL修复**: 先提取symbol变量", "✅ 包含关键修复标记"),
        ("根据Gate.io官方API文档", "✅ 包含官方API文档参考"),
        ("格式1：标准格式", "✅ 包含多格式处理逻辑"),
        ("self._log_data_received", "✅ 包含数据接收日志")
    ]
    
    all_passed = True
    for check_text, success_msg in checks:
        if check_text in content:
            print(success_msg)
        else:
            print(f"❌ 缺少: {check_text}")
            all_passed = False
    
    return all_passed

def simulate_fixed_code():
    """模拟修复后的代码执行"""
    print("\n🎯 修复后代码模拟测试")
    print("=" * 30)
    
    # 模拟修复后的逻辑
    mock_data = {
        't': 1754294031515,
        'lastUpdateId': 5748245865,
        's': 'ADA_USDT',
        'l': '50',
        'bids': [['0.7414', '1006.25']],
        'asks': [['0.7415', '1000.25']]
    }
    
    try:
        print("🔄 模拟修复后的执行顺序:")
        
        def fixed_function():
            # 修复后的逻辑（symbol先定义）
            symbol = ""
            
            # 格式1：标准格式
            if "s" in mock_data:
                symbol = mock_data["s"]
            
            print(f"✅ symbol定义成功: {symbol}")
            print(f"✅ 可以安全使用symbol: {symbol}")
            return symbol
        
        result = fixed_function()
        print(f"✅ 修复验证成功: 返回symbol = '{result}'")
        return True
        
    except Exception as e:
        print(f"❌ 修复验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Gate.io WebSocket Symbol变量Bug修复验证")
    print("=" * 60)
    
    # 验证修复
    fix_verified = verify_gate_ws_fix()
    
    # 检查代码结构
    structure_ok = check_code_structure()
    
    # 模拟测试
    simulation_ok = simulate_fixed_code()
    
    # 总结
    print("\n📊 验证总结")
    print("=" * 20)
    print(f"✅ 修复验证: {'通过' if fix_verified else '失败'}")
    print(f"✅ 结构检查: {'通过' if structure_ok else '失败'}")
    print(f"✅ 模拟测试: {'通过' if simulation_ok else '失败'}")
    
    overall_success = fix_verified and structure_ok and simulation_ok
    print(f"\n🎯 总体结果: {'✅ 修复成功' if overall_success else '❌ 修复失败'}")
    
    # 保存验证结果
    result = {
        "timestamp": time.time(),
        "verification_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "fix_verified": fix_verified,
        "structure_ok": structure_ok,
        "simulation_ok": simulation_ok,
        "overall_success": overall_success,
        "fix_description": "将symbol变量提取逻辑移到_log_data_received调用之前",
        "verified_file": "websocket/gate_ws.py"
    }
    
    result_file = f"gate_websocket_fix_verification_{int(time.time())}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 验证结果已保存: {result_file}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)