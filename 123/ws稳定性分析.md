非常关键的问题，你的描述揭示了典型的**WebSocket 静默断流 + 订阅失效 + 重连订阅恢复** 的连锁效应。

---

## ✅ 你描述的系统行为

1. 启动阶段，**6 个交易对组合都有差价计算**
2. 几分钟后，**只剩 1～2 个组合有数据更新 → 差价也只对这些计算**
3. 某次检测后，**恰好在这2个组合中出现了 ≥ 0.5% 的差价**
4. 系统执行逻辑（如开仓等）
5. 随后，不久后又恢复成 6 个组合都有差价了！

---

## 🧠 根本原因分析：**非对称的 WebSocket 数据活性问题**

| 问题类型                       | 解释                                | 关联你的现象                   |
| -------------------------- | --------------------------------- | ------------------------ |
| ❌ symbol 层级断流（silent drop） | 某些交易对停止推送，但连接还在、ping 还活           | 所以系统“以为它还在”，但实际它不在参与差价计算 |
| ❌ 无活性检测机制                  | 没有检测每个交易对最后更新时间                   | 导致失活 symbol 没有被重连或重新订阅   |
| ❌ 没有自动订阅恢复                 | WebSocket 可能断开重连后你**没有补回所有组合的订阅** | 只剩部分组合在运行，差价也只在那两组被计算    |
| ✅ 差价机制是正常的                 | 所以一旦这两个组合中出现 0.5% 差价，就会执行操作       | 只是 **其它组合处于“死而不知”的状态**   |

---

## ✅ 你看到“突然恢复成 6 个组合”的真正原因是：

> **你系统可能在某个阶段做了 WebSocket 自动重连，或者触发了 reconnect logic → 所有 symbol 被重新订阅 → 数据突然恢复正常 → 差价恢复完整计算。**

---

## 🔥 问题总结图示（关键）：

```text
启动阶段：
    全部 6 组合订阅成功 ✅
几分钟后：
    有些 symbol silent drop ❌（你未检测，未处理）
    差价逻辑只能计算 2 个组合 😢
期间：
    某组合达到差价阈值 → 执行操作 ✅
    但其它潜在组合被静默忽略
稍后：
    reconnect or ping timeout 触发自动重连
    → 所有 symbol 恢复 → 差价组合又完整了
```

---

# ✅ 正确解决方式（必须做）

| 模块                        | 是否必须 | 原因                                                               |
| ------------------------- | ---- | ---------------------------------------------------------------- |
| ✅ **symbol 活性检测**         | 必须   | 每个 symbol 都维护 `last_message_time`，超 30 秒就判定断流，强制重连订阅             |
| ✅ **重连自动补订阅机制**           | 必须   | WebSocket 被断开 → reconnect 后要重新订阅所有 symbol（否则只活几个）                |
| ✅ **差价计算前验证 symbol 是否活跃** | 推荐   | 没数据的 symbol 不应该参与差价逻辑，否则误判                                       |
| ✅ **状态日志输出**              | 推荐   | 输出 `[INACTIVE][symbol]`, `[RECONNECT]`, `[RESUBSCRIBE]`，方便监控系统健康 |

---

# ✅ 实战修复建议（关键实现）

```python
# 差价计算前的 symbol 数据检查：
for pair in all_combinations:
    if now - last_msg_time[pair.symbol1] > 30 or now - last_msg_time[pair.symbol2] > 30:
        log(f"[INACTIVE][{pair.symbol1}-{pair.symbol2}] 跳过差价计算")
        continue
    compute_spread(pair)
```

---

# ✅ 总结一句话：

> 你看到的数据组合减少 → 差价计算异常 → 又恢复 —— 本质是：**WebSocket symbol 级别断流 + 没有重订阅补全**。

---
