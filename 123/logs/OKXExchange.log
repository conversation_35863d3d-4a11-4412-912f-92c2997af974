2025-08-04 11:44:46.614 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 1次/秒
2025-08-04 11:44:46 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-04 11:44:46 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-04 11:44:46 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-04 11:44:46 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-04 11:44:46.614 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=1次/秒
2025-08-04 11:44:46 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-04 11:44:46 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-04 11:44:46 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-04 11:44:47 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-04 11:44:47.257 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:44:47.742 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:44:47.742 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:44:48.299 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:44:48.299 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:44:48 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-04 11:44:48.299 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:44:48.766 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:44:48.766 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:44:49.247 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:44:49.247 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:44:49 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-04 11:44:49.247 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:44:49.741 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:44:49.741 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:44:50.240 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:44:50.240 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:44:50 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-04 11:44:50.240 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:44:50.771 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:44:50.771 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:44:51.252 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:44:51.252 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:44:51 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-04 11:44:51 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-04 11:44:51.762 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.772 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.773 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.773 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.774 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.782 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 11:44:51.787 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 11:45:17.008 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:17.008 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:45:18.002 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:18.002 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:45:19.016 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:19.017 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:45:20.009 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:20.009 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:45:21.011 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:21.011 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:45:22.030 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:45:22.030 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:06.553 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 11:46:11.606 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 11:46:13.118 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 11:46:13.617 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 11:46:13.617 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 11:46:13.633 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 11:46:15.712 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 11:46:17.135 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.137 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.137 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.137 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.137 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.138 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SHIB-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.138 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-04 11:46:17.281 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:17.281 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:17.707 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:17.707 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:17.711 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:17.711 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:17.715 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:17.715 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:17.715 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 11:46:17.715 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 11:46:17 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 11:46:17 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-04 11:46:17.726 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:17.727 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:17.736 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:17.736 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:18.213 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:18.213 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:18.218 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:18.219 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:18.219 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:18.219 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:18.222 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:18.222 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:19.800 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:19.800 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:20.300 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:20.300 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:20.301 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-04 11:46:20.301 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-04 11:46:20.833 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-04 11:46:20.833 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-04 11:46:39.164 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.661 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.664 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.665 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.669 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.674 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 11:46:39.689 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.014 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.480 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.485 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.490 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.495 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.504 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-04 11:46:47.505 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
