2025-08-04 11:44:52.901 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:44:54.524 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:44:55.880 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:44:55.881 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_SHIB-USDT | 获取合约信息失败，所有重试都失败
2025-08-04 11:45:16.927 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 交易对不存在: 10001: params error: symbol invalid SHIBUSDT
2025-08-04 11:46:09.537 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:17.715 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 11:46:17.715 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 11:46:17.716 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-04 11:46:17.723 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-04 11:46:17.724 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-04 11:46:19.873 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: 交易对不存在: 10001: params error: symbol invalid SHIBUSDT
2025-08-04 11:46:39.190 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 交易对不存在: 10001: params error: symbol invalid SHIBUSDT
2025-08-04 11:46:40.620 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:42.118 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:43.620 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:43.620 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_SHIB-USDT | 获取合约信息失败，所有重试都失败
2025-08-04 11:46:44.100 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-04 11:46:44.101 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-04 11:46:48.280 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:48.362 [ERROR] [core.unified_leverage_manager] ❌ Bybit杠杆设置异常: 交易对不存在: 10001: params error: symbol invalid SHIBUSDT
2025-08-04 11:46:49.781 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:51.282 [ERROR] [exchanges.bybit_exchange] ❌ Bybit获取合约信息失败 SHIB-USDT: 交易对不存在: 10001: params error: symbol invalid
2025-08-04 11:46:51.282 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: bybit_SHIB-USDT | 获取合约信息失败，所有重试都失败
2025-08-04 11:47:03.363 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-04 11:47:03.364 [ERROR] [websocket] [BYBIT] orderbook订阅失败
2025-08-04 11:47:14.086 [ERROR] [OpportunityScanner] 🚨 健康检查告警: Bybit期货数据键为0
2025-08-04 11:47:14.086 [ERROR] [OpportunityScanner] 🚨 这将导致套利机会检测失败
2025-08-04 11:47:14.086 [ERROR] [OpportunityScanner] 📊 当前数据状态: 总键数=35, Bybit期货键数=0
2025-08-04 11:48:09.587 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:09.591 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:11.179 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:11.601 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:11.602 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:15.600 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:15.600 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:15.964 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:16.395 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:16.397 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:17.020 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:17.021 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:18.983 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:19.658 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:19.659 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:20.143 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:20.536 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:20.841 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:21.084 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:21.086 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:21.302 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:21.303 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:21.304 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:22.087 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:24.743 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:25.370 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:25.550 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:25.550 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:25.551 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:25.728 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:26.317 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:26.319 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.507 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.512 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.703 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.703 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.903 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:29.927 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:30.323 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:30.740 [ERROR] [websocket] [OKX] 连接错误: server rejected WebSocket connection: HTTP 503
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 281, in _connect
    self.ws = await asyncio.wait_for(
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 503
2025-08-04 11:48:30.750 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:30.750 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:30.958 [ERROR] [websocket.error_handler] [OKX] connection_error: server rejected WebSocket connection: HTTP 503
2025-08-04 11:48:30.988 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:32.549 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:32.550 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:33.983 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:34.195 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:34.837 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:35.283 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:36.339 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:36.432 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:39.355 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:39.355 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:39.356 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.076 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.077 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.078 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.381 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.382 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.632 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.633 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.633 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.836 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.837 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:40.837 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:42.037 [ERROR] [websocket] [GATE] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:43.350 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:43.774 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:43.775 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:44.142 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:44.143 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:44.144 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
2025-08-04 11:48:44.144 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message
Traceback (most recent call last):
  File "/root/myproject/123/70 gate和okx还是数据阻塞/123/websocket/ws_client.py", line 674, in run
    message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "/root/.local/lib/python3.12/site-packages/websockets/legacy/protocol.py", line 532, in recv
    raise RuntimeError(
RuntimeError: cannot call recv while another coroutine is already waiting for the next message
